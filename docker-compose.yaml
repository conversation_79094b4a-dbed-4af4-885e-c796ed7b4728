version: '3.8'

services:
  db:
    image: postgres:15
    restart: always
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - db_data:/var/lib/postgresql/data
      - ./schema.sql:/docker-entrypoint-initdb.d/schema.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  api:
    build:
      context: .
      dockerfile: API.Dockerfile
    restart: unless-stopped
    depends_on:
      db:
        condition: service_healthy
    volumes:
      - /dev/shm:/dev/shm
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8000/health || exit 1"]
      interval: 5s
      timeout: 5s
      retries: 5

  scraper:
    build:
      context: .
      dockerfile: Scraper.Dockerfile
    restart: unless-stopped
    environment:
      - DATABASE_URL=**************************************/postgres
      - PUMP_URL=https://pump.fun/coin/EpxanDRMd9iDEYdozB2CBV6tuS5mnYqBLuXrRbNcpump
      - HEADLESS=true
      - INTERVAL=5
      - ENABLE_JSON_SCRAPING=true
    depends_on:
      api:
        condition: service_healthy
    volumes:
      - /dev/shm:/dev/shm

  websocket:
    build:
      context: .
      dockerfile: Websocket.Dockerfile
    restart: unless-stopped
    environment:
      - DATABASE_URL=**************************************/postgres
      - ROOM_ID=EpxanDRMd9iDEYdozB2CBV6tuS5mnYqBLuXrRbNcpump
      - PUMP_WS=wss://pump.fun
    depends_on:
      api:
        condition: service_healthy

volumes:
  db_data: