"""
Configuration management for the Pump Plays Games API
"""
import os
from typing import Optional


class Settings:
    """Application settings"""
    
    def __init__(self):
        # Database
        self.database_url = os.getenv("DATABASE_URL", "postgresql://postgres:password@localhost:5432/postgres")

        # API
        self.api_host = os.getenv("API_HOST", "0.0.0.0")
        self.api_port = int(os.getenv("API_PORT", "8000"))
        self.api_workers = int(os.getenv("API_WORKERS", "1"))

        # Logging
        self.log_level = os.getenv("LOG_LEVEL", "INFO")

        # Chat
        self.max_chat_messages = int(os.getenv("MAX_CHAT_MESSAGES", "100"))
        self.recent_chat_messages = int(os.getenv("RECENT_CHAT_MESSAGES", "50"))

        # WebSocket
        self.websocket_ping_interval = int(os.getenv("WEBSOCKET_PING_INTERVAL", "30"))


# Global settings instance
settings = Settings()


def get_db_connection_params() -> dict:
    """Parse database URL and return connection parameters for psycopg2"""
    database_url = settings.database_url
    
    if database_url.startswith("postgresql://"):
        # Extract connection parameters
        url_parts = database_url.replace("postgresql://", "").split("@")
        user_pass = url_parts[0].split(":")
        host_db = url_parts[1].split("/")
        host_port = host_db[0].split(":")
        
        return {
            "host": host_port[0],
            "port": int(host_port[1]) if len(host_port) > 1 else 5432,
            "database": host_db[1],
            "user": user_pass[0],
            "password": user_pass[1] if len(user_pass) > 1 else ""
        }
    else:
        # Fallback to direct connection
        return {
            "host": "localhost",
            "port": 5432,
            "database": "postgres",
            "user": "postgres",
            "password": "password"
        }
