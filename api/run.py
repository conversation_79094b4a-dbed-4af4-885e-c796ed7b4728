#!/usr/bin/env python3
"""
Startup script for the Blackjack Plays API
"""
import os
import uvicorn

if __name__ == "__main__":
    # Load environment variables
    host = os.getenv("API_HOST", "0.0.0.0")
    port = int(os.getenv("API_PORT", "8000"))
    workers = int(os.getenv("API_WORKERS", "1"))
    log_level = os.getenv("LOG_LEVEL", "info").lower()
    
    print(f"Starting Blackjack Plays API on {host}:{port}")
    print(f"Workers: {workers}")
    print(f"Log level: {log_level}")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        workers=workers,
        log_level=log_level,
        reload=False  # Disable reload for production
    )
