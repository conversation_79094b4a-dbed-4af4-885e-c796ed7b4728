"""
Blackjack Plays API - FastAPI application for the blackjack game
"""
import os
import logging
from typing import Dict, List, Optional
from contextlib import asynccontextmanager

import psycopg2
from psycopg2.extras import RealDictCursor
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from pydantic import BaseModel

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global state for shared memory between requests
class AppState:
    def __init__(self):
        self.db_connection: Optional[psycopg2.connection] = None
        self.connected_websockets: List[WebSocket] = []
        self.chat_messages: List[Dict] = []
        self.game_state: Dict = {
            "current_game_id": None,
            "game_active": False,
            "player_cards": [],
            "dealer_cards": [],
            "votes": {}
        }

app_state = AppState()

# Pydantic models
class ChatMessage(BaseModel):
    username: str
    message: str
    timestamp: Optional[str] = None

class GameAction(BaseModel):
    action: str  # 'hit', 'stand', 'double', 'split'
    username: str

# Database connection
def get_db_connection():
    """Get database connection with retry logic"""
    try:
        if app_state.db_connection is None or app_state.db_connection.closed:
            database_url = os.getenv(
                "DATABASE_URL", 
                "postgresql://postgres:password@localhost:5432/postgres"
            )
            
            # Parse the database URL for psycopg2
            if database_url.startswith("postgresql://"):
                # Extract connection parameters
                url_parts = database_url.replace("postgresql://", "").split("@")
                user_pass = url_parts[0].split(":")
                host_db = url_parts[1].split("/")
                host_port = host_db[0].split(":")
                
                conn_params = {
                    "host": host_port[0],
                    "port": int(host_port[1]) if len(host_port) > 1 else 5432,
                    "database": host_db[1],
                    "user": user_pass[0],
                    "password": user_pass[1] if len(user_pass) > 1 else ""
                }
            else:
                # Fallback to direct connection
                conn_params = {
                    "host": "localhost",
                    "port": 5432,
                    "database": "postgres",
                    "user": "postgres",
                    "password": "password"
                }
            
            app_state.db_connection = psycopg2.connect(**conn_params)
            app_state.db_connection.autocommit = True
            logger.info("Database connection established")
            
        return app_state.db_connection
    except Exception as e:
        logger.error(f"Failed to connect to database: {e}")
        raise HTTPException(status_code=500, detail="Database connection failed")

# Lifespan context manager
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("Starting up Blackjack Plays API...")
    try:
        # Initialize database connection
        get_db_connection()
        logger.info("Database connection initialized")
    except Exception as e:
        logger.error(f"Failed to initialize database: {e}")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Blackjack Plays API...")
    if app_state.db_connection and not app_state.db_connection.closed:
        app_state.db_connection.close()
        logger.info("Database connection closed")

# Create FastAPI app
app = FastAPI(
    title="Blackjack Plays API",
    description="API for the Blackjack Plays game - like Twitch Plays Pokemon but for Blackjack!",
    version="1.0.0",
    lifespan=lifespan
)

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()
        
        return {
            "status": "healthy",
            "database": "connected",
            "active_connections": len(app_state.connected_websockets),
            "chat_messages_count": len(app_state.chat_messages)
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "database": "disconnected",
            "error": str(e)
        }

# Chat message endpoint
@app.post("/chat")
async def receive_chat_message(message: ChatMessage):
    """Receive a new chat message"""
    try:
        # Add timestamp if not provided
        import datetime
        if not message.timestamp:
            message.timestamp = datetime.datetime.now().isoformat()
        
        # Store in shared memory
        chat_data = message.dict()
        app_state.chat_messages.append(chat_data)
        
        # Keep only last 100 messages in memory
        if len(app_state.chat_messages) > 100:
            app_state.chat_messages = app_state.chat_messages[-100:]
        
        # Broadcast to all connected websockets
        if app_state.connected_websockets:
            message_data = {
                "type": "chat_message",
                "data": chat_data
            }
            
            # Remove disconnected websockets
            active_websockets = []
            for websocket in app_state.connected_websockets:
                try:
                    await websocket.send_json(message_data)
                    active_websockets.append(websocket)
                except:
                    # WebSocket is disconnected
                    pass
            app_state.connected_websockets = active_websockets
        
        logger.info(f"Chat message received from {message.username}: {message.message}")
        
        return {
            "status": "success",
            "message": "Chat message received",
            "broadcast_count": len(app_state.connected_websockets)
        }
        
    except Exception as e:
        logger.error(f"Error processing chat message: {e}")
        raise HTTPException(status_code=500, detail="Failed to process chat message")

# Get recent chat messages
@app.get("/chat/recent")
async def get_recent_chat():
    """Get recent chat messages"""
    return {
        "messages": app_state.chat_messages[-50:],  # Last 50 messages
        "total_count": len(app_state.chat_messages)
    }

# Game state endpoint
@app.get("/game/state")
async def get_game_state():
    """Get current game state"""
    return {
        "game_state": app_state.game_state,
        "connected_clients": len(app_state.connected_websockets)
    }

# WebSocket endpoint for frontend
@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time communication with frontend"""
    await websocket.accept()
    app_state.connected_websockets.append(websocket)
    
    logger.info(f"WebSocket connected. Total connections: {len(app_state.connected_websockets)}")
    
    try:
        # Send initial state
        await websocket.send_json({
            "type": "connection_established",
            "data": {
                "game_state": app_state.game_state,
                "recent_messages": app_state.chat_messages[-10:],
                "connected_clients": len(app_state.connected_websockets)
            }
        })
        
        # Keep connection alive and handle incoming messages
        while True:
            data = await websocket.receive_json()
            
            # Handle different message types
            if data.get("type") == "ping":
                await websocket.send_json({"type": "pong"})
            elif data.get("type") == "game_action":
                # Handle game actions (voting, etc.)
                action_data = data.get("data", {})
                logger.info(f"Game action received: {action_data}")
                
                # Broadcast action to all clients
                broadcast_data = {
                    "type": "game_action",
                    "data": action_data
                }
                
                for ws in app_state.connected_websockets:
                    try:
                        if ws != websocket:  # Don't send back to sender
                            await ws.send_json(broadcast_data)
                    except:
                        pass
            
    except WebSocketDisconnect:
        logger.info("WebSocket disconnected")
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
    finally:
        # Remove from connected websockets
        if websocket in app_state.connected_websockets:
            app_state.connected_websockets.remove(websocket)
        logger.info(f"WebSocket removed. Total connections: {len(app_state.connected_websockets)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        workers=1,  # Single worker to ensure shared memory works
        reload=False
    )
