import asyncio
import json
import os
import psycopg2
from psycopg2.extras import RealDictCursor
import websockets
import aiohttp
from datetime import datetime
import concurrent.futures


class PumpFunWebSocket:
    def __init__(self, room_id, session: aiohttp.ClientSession, ws_url=None):
        self.room_id = room_id
        self.session = session
        self.api_url = 'http://localhost:8000/chat'
        self.ws_url = ws_url or os.getenv('PUMP_WS', 'wss://livechat.pump.fun/socket.io/?EIO=4&transport=websocket')
        self.socket = None
        self.valid_moves = {'hit', 'stand', 'double', 'split'}
        self.db_connection = None
        self.executor = concurrent.futures.ThreadPoolExecutor(max_workers=2)

    async def init_db(self):
        """Initialize PostgreSQL connection"""
        try:
            db_url = os.getenv('DATABASE_URL', 'postgresql://postgres:password@localhost/postgres')
            self.db_connection = psycopg2.connect(db_url)
            print("✅ Database connection established")
        except Exception as e:
            print(f"❌ Database connection failed: {e}")
    

    async def connect(self):
        headers = {
            'Origin': 'https://pump.fun',
            'User-Agent': 'Mozilla/5.0 (Linux; Android 15; SM-S931B Build/AP3A.240905.015.A2; wv) AppleWebKit/537.36'
        }
        
        await self.init_db()
        
        self.socket = await websockets.connect(self.ws_url, additional_headers=headers)
        print(f"✅ Connected to WebSocket for room: {self.room_id}")
        
        asyncio.create_task(self._handle_messages())

    async def _handle_messages(self):
        async for message in self.socket:
            await self._process_message(message)

    async def _process_message(self, message):
        if message == '2':
            await self.socket.send('3')
        elif message.startswith('0'):
            payload = {'origin': 'https://pump.fun', 'timestamp': int(datetime.now().timestamp() * 1000), 'token': None}
            await self.socket.send('40' + json.dumps(payload))
        elif message.startswith('40'):
            await self.socket.send('42' + json.dumps(['joinRoom', {'roomId': self.room_id, 'username': ''}]))
            print(f"Joined room: {self.room_id}")
        elif message.startswith('42'):
            try:
                data = json.loads(message[2:])
                if len(data) >= 2 and data[0] in ['newMessage', 'message']:
                    await self._handle_chat(data[1])
            except Exception as E:
                print(f"❌ Error processing message: {E}")
                pass

    def _upsert_user_sync(self, username, wallet_address):
        """Synchronous database operation for upserting user"""
        if not self.db_connection:
            return False
        
        try:
            with self.db_connection.cursor() as cursor:
                cursor.execute("""
                    INSERT INTO users (username, wallet_address, created_at, updated_at)
                    VALUES (%s, %s, NOW(), NOW())
                    ON CONFLICT (username) DO UPDATE SET
                        wallet_address = COALESCE(EXCLUDED.wallet_address, users.wallet_address),
                        updated_at = NOW()
                """, (username, wallet_address))
                
                self.db_connection.commit()
                return True
                
        except Exception as e:
            print(f"❌ Error upserting user {username}: {e}")
            self.db_connection.rollback()
            return False
    
    async def upsert_user(self, username, wallet_address, message):
        """Non-blocking user upsert operation"""
        # Send user data to API
        async with self.session.post(self.api_url, json={'username': username, 'message': message}) as response:
            if response.status != 200:
                print(f"❌ Error sending user data to API: {await response.text()}")
            else:
                print(f"✅ Sent message from {username} to API: {message}")

        loop = asyncio.get_event_loop()
        try:
            await loop.run_in_executor(self.executor, self._upsert_user_sync, username, wallet_address)
        except Exception as e:
            print(f"❌ Error in async user upsert: {e}")

    async def _handle_chat(self, data):
        message = data.get('message', '').lower().strip()
        username = data.get('username', 'unknown')
        user_address = data.get('userAddress')
        
        # Upsert user in background (non-blocking)
        if username and username != 'unknown':
            asyncio.create_task(self.upsert_user(username, user_address, message))
        
        # Just print valid moves to console
        if message in self.valid_moves:
            print(f"🎮 Valid move: '{message}' from {username}")

    async def close(self):
        """Clean up resources"""
        if self.socket:
            await self.socket.close()
        if self.executor:
            self.executor.shutdown(wait=True)
        if self.db_connection:
            self.db_connection.close()

    async def run(self):
        try:
            await self.connect()
            await self.socket.wait_closed()
        except KeyboardInterrupt:
            print("🛑 Shutting down WebSocket service...")
        finally:
            await self.close()


async def main():
    async with aiohttp.ClientSession() as session:
        room_id = os.getenv('ROOM_ID', 'EpxanDRMd9iDEYdozB2CBV6tuS5mnYqBLuXrRbNcpump')
        client = PumpFunWebSocket(room_id, session)
        await client.run()


if __name__ == "__main__":
    asyncio.run(main())